apiVersion: 2021-03-01
location: eastus
name: redash-dev-v2
properties:
  containers:
    - name: postgres
      properties:
        image: redashacr.azurecr.io/postgres:13-alpine
        resources:
          requests:
            cpu: 0.5
            memoryInGB: 1.5
        environmentVariables:
          - name: POSTGRES_USER
            value: redash
          - name: POSTGRES_PASSWORD
            value: redash
          - name: POSTGRES_DB
            value: redash
        ports:
          - port: 5432
        volumeMounts:
          - name: postgres-data
            mountPath: /var/lib/postgresql/data

    - name: redis
      properties:
        image: redashacr.azurecr.io/redis:7-alpine
        resources:
          requests:
            cpu: 0.2
            memoryInGB: 0.5
        ports:
          - port: 6379

    - name: redash-create-db
      properties:
        image: redashacr.azurecr.io/redash:latest
        command:
          - /bin/sh
          - -c
          - |
            echo "Waiting for Postgres..." && \
            until pg_isready -h localhost -p 5432 -U redash; do echo "Postgres not ready, retrying in 5s..."; sleep 5; done; \
            echo "Waiting for Redis..." && \
            until (echo PING | nc localhost 6379 >/dev/null); do echo "Redis not ready, retrying in 5s..."; sleep 5; done; \
            echo "Running database migrations..." && \
            /app/manage.py database migrate && \
            echo "Creating tables (if missing)..." && \
            /app/manage.py database create_tables && \
            echo "Migrations complete. Exiting."
        resources:
          requests:
            cpu: 0.2
            memoryInGB: 0.5
        environmentVariables:
          - name: REDASH_REDIS_URL
            value: redis://localhost:6379/0
          - name: REDASH_DATABASE_URL
            value: postgresql://redash:redash@localhost:5432/redash
          - name: REDASH_COOKIE_SECRET
            value: veryverysecret
          - name: REDASH_SECRET_KEY
            value: secretkey

    - name: redash
      properties:
        image: redashacr.azurecr.io/redash:latest
        command:
          - /bin/sh
          - -c
          - |
            echo "Waiting for DB setup..." && \
            sleep 20 && \
            /app/bin/docker-entrypoint server
        ports:
          - port: 5000
        resources:
          requests:
            cpu: 0.5
            memoryInGB: 1.0
        environmentVariables:
          - name: REDASH_REDIS_URL
            value: redis://localhost:6379/0
          - name: REDASH_DATABASE_URL
            value: postgresql://redash:redash@localhost:5432/redash
          - name: REDASH_COOKIE_SECRET
            value: veryverysecret
          - name: REDASH_SECRET_KEY
            value: secretkey
          - name: REDASH_WEB_WORKERS
            value: "4"

    - name: api
      properties:
        image: redashacr.azurecr.io/redash-api:v2
        resources:
          requests:
            cpu: 0.5
            memoryInGB: 1.0
        ports:
          - port: 3000
        environmentVariables:
          - name: NODE_ENV
            value: development
          - name: PORT
            value: "3000"
          - name: ALLOWED_ORIGINS
            value: http://localhost:5000,http://localhost:8080
          - name: API_KEY
            value: redash-api-key-2024
          - name: COSMOS_DB_ENDPOINT
            value: https://emr-dev-cosmosdb.documents.azure.com:443/
          - name: COSMOS_DB_KEY
            value: ****************************************************************************************
          - name: COSMOS_DB_DATABASE
            value: ArcaAudioLayer
          - name: JWT_SECRET
            value: your-jwt-secret-key-here
          - name: RATE_LIMIT_WINDOW_MS
            value: "900000"
          - name: RATE_LIMIT_MAX_REQUESTS
            value: "100"
          - name: LOG_LEVEL
            value: info

    - name: redash-scheduler
      properties:
        image: redashacr.azurecr.io/redash:latest
        command:
          - /bin/sh
          - -c
          - |
            echo "Waiting for DB setup..." && \
            sleep 20 && \
            /app/bin/docker-entrypoint scheduler
        resources:
          requests:
            cpu: 0.3
            memoryInGB: 0.5
        environmentVariables:
          - name: REDASH_REDIS_URL
            value: redis://localhost:6379/0
          - name: REDASH_DATABASE_URL
            value: postgresql://redash:redash@localhost:5432/redash
          - name: REDASH_COOKIE_SECRET
            value: veryverysecret
          - name: REDASH_SECRET_KEY
            value: secretkey

    - name: redash-scheduled-worker
      properties:
        image: redashacr.azurecr.io/redash:latest
        command:
          - /bin/sh
          - -c
          - |
            echo "Waiting for DB setup..." && \
            sleep 20 && \
            /app/bin/docker-entrypoint worker
        resources:
          requests:
            cpu: 0.3
            memoryInGB: 0.5
        environmentVariables:
          - name: REDASH_REDIS_URL
            value: redis://localhost:6379/0
          - name: REDASH_DATABASE_URL
            value: postgresql://redash:redash@localhost:5432/redash
          - name: REDASH_COOKIE_SECRET
            value: veryverysecret
          - name: REDASH_SECRET_KEY
            value: secretkey
          - name: QUEUES
            value: scheduled_queries,schemas

    - name: redash-adhoc-worker
      properties:
        image: redashacr.azurecr.io/redash:latest
        command:
          - /bin/sh
          - -c
          - |
            echo "Waiting for DB setup..." && \
            sleep 20 && \
            /app/bin/docker-entrypoint worker
        resources:
          requests:
            cpu: 0.3
            memoryInGB: 0.5
        environmentVariables:
          - name: REDASH_REDIS_URL
            value: redis://localhost:6379/0
          - name: REDASH_DATABASE_URL
            value: postgresql://redash:redash@localhost:5432/redash
          - name: REDASH_COOKIE_SECRET
            value: veryverysecret
          - name: REDASH_SECRET_KEY
            value: secretkey
          - name: QUEUES
            value: queries

  osType: Linux
  ipAddress:
    type: Public
    dnsNameLabel: redash-dev-v2  # choose a unique name
    ports:
      - protocol: tcp
        port: 80
        targetPort: 5000
      - protocol: tcp
        port: 3000
    
  imageRegistryCredentials:
    - server: redashacr.azurecr.io
      username: redashacr
      password: ****************************************************
  volumes:
    - name: postgres-data
      emptyDir: {}
  restartPolicy: Always



