version: '3.8'

services:
  postgres:
    image: redashacr.azurecr.io/postgres:13-alpine
    environment:
      POSTGRES_USER: redash
      POSTGRES_PASSWORD: redash
      POSTGRES_DB: redash
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U redash"]
      interval: 30s
      timeout: 10s
      retries: 5

  redis:
    image: redashacr.azurecr.io/redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  redash_create_db:
    image: redashacr.azurecr.io/redash:latest
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
    command: create_db
    volumes:
      - redash_data:/app
    restart: "no"

  redash:
    image: redashacr.azurecr.io/redash:latest
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      redash_create_db:
        condition: service_completed_successfully
    ports:
      - "5000:5000"
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_WEB_WORKERS: 4
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"
      REDASH_CORS_ACCESS_CONTROL_ALLOW_ORIGIN: "http://localhost:3000"
      REDASH_CORS_ACCESS_CONTROL_ALLOW_CREDENTIALS: "true"
    restart: unless-stopped
    command: server
    volumes:
      - redash_data:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  api:
    image: redashacr.azurecr.io/redash-api:v2
    environment:
      NODE_ENV: development
      PORT: 3000
      ALLOWED_ORIGINS: http://localhost:5000
      API_KEY: redash-api-key-2024
      COSMOS_DB_ENDPOINT: https://emr-dev-cosmosdb.documents.azure.com:443/
      COSMOS_DB_KEY: ****************************************************************************************
      COSMOS_DB_DATABASE: ArcaAudioLayer
      JWT_SECRET: your-jwt-secret-key-here
      RATE_LIMIT_WINDOW_MS: "900000"
      RATE_LIMIT_MAX_REQUESTS: "100"
      LOG_LEVEL: info
    ports:
      - "3000:3000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 15s
      timeout: 5s
      retries: 5

  redash_scheduler:
    image: redashacr.azurecr.io/redash:latest
    depends_on:
      redash:
        condition: service_healthy
      redash_create_db:
        condition: service_completed_successfully
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"
    restart: unless-stopped
    command: scheduler
    volumes:
      - redash_data:/app

  redash_scheduled_worker:
    image: redashacr.azurecr.io/redash:latest
    depends_on:
      redash:
        condition: service_healthy
      redash_create_db:
        condition: service_completed_successfully
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"
      QUEUES: "scheduled_queries,schemas"
    restart: unless-stopped
    command: worker
    volumes:
      - redash_data:/app

  redash_adhoc_worker:
    image: redashacr.azurecr.io/redash:latest
    depends_on:
      redash:
        condition: service_healthy
      redash_create_db:
        condition: service_completed_successfully
    environment:
      PYTHONUNBUFFERED: 0
      REDASH_LOG_LEVEL: INFO
      REDASH_REDIS_URL: redis://redis:6379/0
      REDASH_DATABASE_URL: ******************************************
      REDASH_COOKIE_SECRET: veryverysecret
      REDASH_SECRET_KEY: secretkey
      REDASH_ALLOW_PRIVATE_ADDRESSES: "true"
      REDASH_ENFORCE_PRIVATE_IP_BLOCK: "false"
      QUEUES: "queries"
    restart: unless-stopped
    command: worker
    volumes:
      - redash_data:/app

volumes:
  postgres_data:
  redash_data:

networks:
  default:
    name: redash_network
