const cosmosDbClient = require('../config/cosmosdb');

class UserService {
  async getUserById(userId) {
    try {
      console.log(`Fetching user by ID: ${userId}`);
      
      // First try direct ID lookup
      try {
        const { resource: user } = await cosmosDbClient.database('ArcaaiEMR')
          .container('Users')
          .item(userId, userId)
          .read();
        
        if (user) {
          return user;
        }
      } catch (directError) {
        console.log(`Direct ID lookup failed for ${userId}, trying B2C user ID search`);
      }
      
      // If direct lookup fails, try B2C user ID search
      const querySpec = {
        query: 'SELECT * FROM c WHERE c.b2cUserId = @userId',
        parameters: [
          {
            name: '@userId',
            value: userId
          }
        ]
      };
      
      const { resources: users } = await cosmosDbClient.database('ArcaaiEMR')
        .container('Users')
        .items
        .query(querySpec)
        .fetchAll();
      
      return users && users.length > 0 ? users[0] : null;
      
    } catch (error) {
      console.error(`Error fetching user by ID ${userId}:`, error);
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  async getUserOrganization(userId) {
    try {
      console.log(`Getting organization for user: ${userId}`);
      
      const user = await this.getUserById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      if (!user.organizationId) {
        throw new Error('User organization not found');
      }
      
      return user.organizationId;
      
    } catch (error) {
      console.error(`Error getting user organization for ${userId}:`, error);
      throw new Error(`Failed to get user organization: ${error.message}`);
    }
  }
}

module.exports = new UserService();
