const dashboardRepo = require('../repositories/dashboardRepo');

module.exports = {
  async getTotalPatients(oid) {
    return dashboardRepo.getTotalPatients(oid);
  },
  async getTotalAppointments(oid) {
    return dashboardRepo.getTotalAppointments(oid);
  },
  async getPatientQueue(oid) {
    return dashboardRepo.getPatientQueue(oid);
  },
  async getAverageWaitingTime(oid) {
    return dashboardRepo.getAverageWaitingTime(oid);
  },
  async getUpcomingAppointments(organizationId, date, doctorId, patientSearch) {
    return await dashboardRepo.getUpcomingAppointments(organizationId, date, doctorId, patientSearch);
  },
  async getAvgConsultationTimeByDepartment(organizationId) {
    return await dashboardRepo.getAvgConsultationTimeByDepartment(organizationId);
  },
  async getDoctors(organizationId) {
    return await dashboardRepo.getDoctors(organizationId);
  }
};
