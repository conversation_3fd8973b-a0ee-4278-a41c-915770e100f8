const cosmosDbClient = require('../config/cosmosdb');

function getDateRange(filter) {
  const now = new Date();
  let startDate = new Date();
  switch (filter) {
    case '7days':
      startDate.setDate(now.getDate() - 6);
      break;
    case '15days':
      startDate.setDate(now.getDate() - 14);
      break;
    case '1month':
      startDate.setDate(now.getDate() - 29);
      break;
  }
  return { startDate, nowDate: now };
}

async function getNoShowRates({ organizationId, filter = '7days' }) {
  const { startDate, nowDate } = getDateRange(filter);
  const noShowRatesRepository = require('../repositories/noShowRatesRepository');
  const noShowRawResult = await noShowRatesRepository.getNoShowRatesRaw({ organizationId, startDate });

  const dailyStats = {};
  noShowRawResult.forEach(item => {
    const date = new Date(item.created_on).toISOString().split('T')[0];
    if (!dailyStats[date]) {
      dailyStats[date] = { total: 0, noShows: 0 };
    }
    dailyStats[date].total += 1;
    if (item.status === 'Cancelled-NoShow') {
      dailyStats[date].noShows += 1;
    }
  });

  // Generate all dates in the range
  const allDates = [];
  let currentDate = new Date(startDate);
  while (currentDate <= nowDate) {
    const dateStr = currentDate.toISOString().split('T')[0];
    allDates.push(dateStr);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  const noShowData = allDates.map(date => {
    const stats = dailyStats[date] || { total: 0, noShows: 0 };
    return {
      date: new Date(date).getDate().toString().padStart(2, '0'),
      no_show_appointments: stats.noShows,
      total_appointments: stats.total,
      no_show_rate: stats.total > 0 ? Math.round((stats.noShows / stats.total) * 100 * 100) / 100 : 0
    };
  });

  return noShowData;
}

module.exports = { getNoShowRates };
