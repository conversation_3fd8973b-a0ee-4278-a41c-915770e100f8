const physicianDashboardRepo = require('../repositories/physicianDashboardRepo');

module.exports = {
  async getTotalPatients(organizationId, doctorId) {
    return physicianDashboardRepo.getTotalPatients(organizationId, doctorId);
  },
  
  async getTotalAppointments(organizationId, doctorId) {
    return physicianDashboardRepo.getTotalAppointments(organizationId, doctorId);
  },
  
  async getTodaysAppointments(organizationId, doctorId) {
    return physicianDashboardRepo.getTodaysAppointments(organizationId, doctorId);
  },
  
  async getPatientQueue(organizationId, doctorId) {
    return physicianDashboardRepo.getPatientQueue(organizationId, doctorId);
  },
  
  async getAverageWaitingTime(organizationId, doctorId) {
    return physicianDashboardRepo.getAverageWaitingTime(organizationId, doctorId);
  },
  
  async getUpcomingAppointments(organizationId, doctorId, date, patientSearch) {
    return physicianDashboardRepo.getUpcomingAppointments(organizationId, doctorId, date, patientSearch);
  },
  
  async getNoShowRate(organizationId, doctorId, period) {
    // Backwards compatibility - route may still call getNoShowRate
    return physicianDashboardRepo.getAttendanceRate(organizationId, doctorId, period);
  },
  
  async getAttendanceRate(organizationId, doctorId, filter) {
    return physicianDashboardRepo.getAttendanceRate(organizationId, doctorId, filter);
  },
  
  async getAverageConsultationTime(organizationId, doctorId, filter) {
    return physicianDashboardRepo.getAverageConsultationTime(organizationId, doctorId, filter);
  }
};
