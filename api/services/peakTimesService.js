const peakTimesRepository = require('../repositories/peakTimesRepository');
const { convertToHourRange } = require('../utils/convertToHourRange');

async function getPeakTimes({ organizationId, filter = '7days' }) {
  const { startDate, nowDate } = getDateRange(filter);
  const peakTimesRawResult = await peakTimesRepository.getPeakTimesRaw({ organizationId, startDate });

  const timeSlotCounts = {};
  const dailyTimeSlots = {};

  peakTimesRawResult.forEach(item => {
    const timeSlot = item.time;
    const date = new Date(item.created_on).toISOString().split('T')[0];
    const hourRange = convertToHourRange(timeSlot);
    timeSlotCounts[hourRange] = (timeSlotCounts[hourRange] || 0) + 1;
    if (!dailyTimeSlots[date]) {
      dailyTimeSlots[date] = {};
    }
    dailyTimeSlots[date][hourRange] = (dailyTimeSlots[date][hourRange] || 0) + 1;
  });

  const dailyPeakTimes = Object.entries(dailyTimeSlots).map(([date, timeSlots]) => ({
    date,
    timeSlots: Object.entries(timeSlots)
      .map(([timeSlot, count]) => ({ timeSlot, count }))
      .sort((a, b) => b.count - a.count)
  })).sort((a, b) => a.date.localeCompare(b.date));

  const peakTimesData = [];
  const allDates = [];
  let currentDate = new Date(startDate);
  while (currentDate <= nowDate) {
    const dateStr = currentDate.toISOString().split('T')[0];
    allDates.push(dateStr);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  allDates.forEach(date => {
    const dayData = dailyPeakTimes.find(d => d.date === date);
    if (dayData && dayData.timeSlots.length > 0) {
      const peakTimeSlot = dayData.timeSlots.reduce((max, current) =>
        current.count > max.count ? current : max
      );
      peakTimesData.push({
        date: new Date(dayData.date).getDate().toString().padStart(2, '0'),
        peakhours: parseInt(peakTimeSlot.timeSlot.split('-')[0]),
        appointment_count: peakTimeSlot.count
      });
    } else {
      peakTimesData.push({
        date: new Date(date).getDate().toString().padStart(2, '0'),
        peakhours: 0,
        appointment_count: 0
      });
    }
  });
  return peakTimesData;
}

function getDateRange(filter) {
  const now = new Date();
  let startDate = new Date();
  switch (filter) {
    case '7days':
      startDate.setDate(now.getDate() - 6);
      break;
    case '15days':
      startDate.setDate(now.getDate() - 14);
      break;
    case '1month':
      startDate.setDate(now.getDate() - 29);
      break;
  }
  return { startDate, nowDate: now };
}

module.exports = { getPeakTimes };