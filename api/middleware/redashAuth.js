const jwt = require('jsonwebtoken');
const crypto = require('crypto');

/**
 * Middleware to handle Redash-specific authentication
 * Supports multiple token passing methods for iframe and dashboard access
 */
const redashAuthMiddleware = (req, res, next) => {
  try {
    // Extract token from various sources
    let token = null;
    
    // 1. Authorization header (Bearer token)
    if (req.headers.authorization?.startsWith('Bearer ')) {
      token = req.headers.authorization.substring(7);
    }
    
    // 2. X-API-Key header
    if (!token && req.headers['x-api-key']) {
      token = req.headers['x-api-key'];
    }
    
    // 3. Query parameter (for iframe URLs)
    if (!token && req.query.api_key) {
      token = req.query.api_key;
    }
    
    // 4. Query parameter (alternative naming)
    if (!token && req.query.token) {
      token = req.query.token;
    }

    // 5. Check for signed URL parameters
    if (!token && req.query.sig && req.query.expires) {
      return handleSignedUrl(req, res, next);
    }

    if (!token) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Missing authentication token. Provide via Authorization header, X-API-Key header, or api_key/token query parameter.'
      });
    }

    // Verify JWT token
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
      req.authenticated = true;
      req.user = decoded;
      req.userId = decoded.userId || decoded.oid || decoded.sub;
      req.organizationId = decoded.organizationId;
      req.dashboardId = decoded.dashboardId;
      req.accessType = decoded.embedType || decoded.accessType || 'dashboard';
      
      return next();
    } catch (jwtError) {
      console.log('JWT verification failed:', jwtError.message);
      
      // Fallback to API key validation
      if (token === process.env.API_KEY) {
        req.authenticated = true;
        req.apiKey = token;
        req.accessType = 'api';
        return next();
      }
      
      return res.status(401).json({
        error: 'Invalid authentication token',
        message: 'The provided token is invalid or expired.'
      });
    }

  } catch (error) {
    console.error('Redash authentication error:', error);
    return res.status(500).json({
      error: 'Authentication error',
      message: 'An error occurred during authentication.'
    });
  }
};

/**
 * Handle signed URL authentication (cryptographic signatures)
 */
const handleSignedUrl = (req, res, next) => {
  try {
    const { user, org, expires, sig } = req.query;
    const currentTime = Date.now();

    // Check expiration
    if (parseInt(expires) < currentTime) {
      return res.status(401).json({
        error: 'URL expired',
        message: 'The signed URL has expired.'
      });
    }

    // Verify signature
    const signatureData = `${user}:${org}:${req.params.dashboardId || ''}:${expires}`;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.JWT_SECRET || 'your-secret-key')
      .update(signatureData)
      .digest('hex');

    if (sig !== expectedSignature) {
      return res.status(401).json({
        error: 'Invalid signature',
        message: 'The URL signature is invalid.'
      });
    }

    // Set request context
    req.authenticated = true;
    req.userId = user;
    req.organizationId = org;
    req.accessType = 'signed';
    req.signedAccess = true;

    next();

  } catch (error) {
    console.error('Signed URL validation error:', error);
    return res.status(401).json({
      error: 'Signature validation failed',
      message: 'Unable to validate URL signature.'
    });
  }
};

/**
 * Create iframe-specific authentication token
 */
const createIframeToken = (userId, organizationId, dashboardId, expiresIn = '1h') => {
  return jwt.sign(
    {
      userId,
      organizationId,
      dashboardId,
      embedType: 'iframe',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn }
  );
};

/**
 * Create dashboard access token
 */
const createDashboardToken = (userId, organizationId, dashboardId, expiresIn = '24h') => {
  return jwt.sign(
    {
      userId,
      organizationId,
      dashboardId,
      accessType: 'dashboard',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn }
  );
};

/**
 * Middleware to validate iframe embedding permissions
 */
const validateIframeAccess = (req, res, next) => {
  if (req.accessType === 'iframe' || req.user?.embedType === 'iframe') {
    // Additional iframe-specific validation can be added here
    return next();
  }
  
  return res.status(403).json({
    error: 'Iframe access denied',
    message: 'This token is not valid for iframe embedding.'
  });
};

/**
 * Middleware to validate dashboard access permissions
 */
const validateDashboardAccess = (req, res, next) => {
  const allowedAccessTypes = ['dashboard', 'signed', 'api'];
  
  if (allowedAccessTypes.includes(req.accessType)) {
    return next();
  }
  
  return res.status(403).json({
    error: 'Dashboard access denied',
    message: 'This token is not valid for dashboard access.'
  });
};

module.exports = {
  redashAuthMiddleware,
  createIframeToken,
  createDashboardToken,
  validateIframeAccess,
  validateDashboardAccess,
  handleSignedUrl
};
