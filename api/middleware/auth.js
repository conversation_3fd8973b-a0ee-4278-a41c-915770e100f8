const jwt = require('jsonwebtoken');

const authMiddleware = (req, res, next) => {
  try {
    // Check for API key in headers
    const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');
    
    if (!apiKey) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Missing API key. Please provide X-API-Key header or Authorization Bearer token.'
      });
    }
    
    // Try to decode as JWT token first (for user-specific requests)
    try {
      const decoded = jwt.verify(apiKey, process.env.JWT_SECRET || 'your-secret-key');
      req.authenticated = true;
      req.user = decoded;
      req.userId = decoded.oid || decoded.sub || decoded.userId; // Extract user ID from token
      return next();
    } catch (jwtError) {
      // If JWT verification fails, fall back to API key validation
      console.log('JWT verification failed, trying API key validation:', jwtError.message);
    }
    
    // For development, accept the configured API key
    if (process.env.NODE_ENV === 'development' && apiKey === process.env.API_KEY) {
      req.authenticated = true;
      req.apiKey = apiKey;
      return next();
    }
    
    // Default: check against configured API key
    if (apiKey === process.env.API_KEY) {
      req.authenticated = true;
      req.apiKey = apiKey;
      return next();
    }
    
    return res.status(401).json({
      error: 'Invalid authentication',
      message: 'The provided token or API key is not valid.'
    });
    
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      error: 'Authentication error',
      message: 'An error occurred during authentication.'
    });
  }
};

// Optional: Create JWT token (for production use)
const createToken = (payload, expiresIn = '24h') => {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
};

// Optional: Validate JWT token
const validateToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    throw new Error('Invalid token');
  }
};

module.exports = authMiddleware;
module.exports.createToken = createToken;
module.exports.validateToken = validateToken;
