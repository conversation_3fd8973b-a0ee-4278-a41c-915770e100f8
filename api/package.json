{"name": "cosmosdb-redash-api", "version": "1.0.0", "description": "REST API to connect CosmosDB with Redash using JSON data source", "main": "server.js", "packageManager": "npm@11.4.2", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@azure/cosmos": "^4.0.0", "express": "^4.20.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["cosmosdb", "redash", "api", "azure", "analytics"], "author": "ArcaAI", "license": "MIT"}