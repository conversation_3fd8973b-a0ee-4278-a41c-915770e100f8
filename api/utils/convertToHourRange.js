function convertToHourRange(timeString) {
  try {
    const timeMatch = timeString.match(/^([0-9]{1,2}):([0-9]{2})\s*(AM|PM)$/i);
    if (!timeMatch) {
      return 'unknown';
    }
    let hour = parseInt(timeMatch[1]);
    const minute = parseInt(timeMatch[2]);
    const ampm = timeMatch[3].toUpperCase();
    if (ampm === 'PM' && hour !== 12) {
      hour += 12;
    } else if (ampm === 'AM' && hour === 12) {
      hour = 0;
    }
    const nextHour = (hour + 1) % 24;
    return `${hour}-${nextHour}`;
  } catch (error) {
    return 'unknown';
  }
}

module.exports = { convertToHourRange };
