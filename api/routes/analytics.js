const express = require('express');
const { query, body, validationResult } = require('express-validator');
const peakTimesService = require('../services/peakTimesService');
const cosmosDbClient = require('../config/cosmosdb');
const router = express.Router();

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

router.get('/peak-times', [
  query('organizationId').optional().isString(),
  query('filter').optional().isIn(['7days', '15days', '1month']).withMessage('Filter must be: 7days, 15days, or 1month'),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, filter = '7days' } = req.query;
    const result = await peakTimesService.getPeakTimes({ organizationId, filter });
    res.json(result);
  } catch (error) {
    console.error('Peak times analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch peak times analytics',
      message: error.message
    });
  }
});


router.get('/no-show-rates', [
  query('organizationId').optional().isString(),
  query('filter').optional().isIn(['7days', '15days', '1month']).withMessage('Filter must be: 7days, 15days, or 1month'),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, filter = '7days' } = req.query;
    const noShowRatesService = require('../services/noShowRatesService');
    const result = await noShowRatesService.getNoShowRates({ organizationId, filter });
    res.json(result);
  } catch (error) {
    console.error('No-show rates analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch no-show rates analytics',
      message: error.message
    });
  }
});

module.exports = router;
