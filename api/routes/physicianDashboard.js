const express = require('express');
const { query, validationResult } = require('express-validator');
const physicianDashboardService = require('../services/physicianDashboardService');
const userService = require('../services/userService');
const router = express.Router();

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// Helper function to get organizationId and doctorId from authenticated user or query params
const getUserInfo = async (req) => {
  let organizationId = null;
  let doctorId = null;

  if (req.userId) {
    // User is authenticated with JWT token, fetch their organization and use their ID as doctorId
    organizationId = await userService.getUserOrganization(req.userId);
    doctorId = req.userId;
  } else {
    // Fallback to query parameters for API key authentication
    organizationId = req.query.organizationId;
    doctorId = req.query.doctorId;
  }

  return { organizationId, doctorId };
};

router.get('/total-patients', [
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    
    if (!doctorId) {
      return res.status(400).json({ error: 'doctorId is required' });
    }
    
    const totalPatients = await physicianDashboardService.getTotalPatients(organizationId, doctorId);
    res.json({ totalPatients });
  } catch (error) {
    console.error('Physician total patients error:', error.message);
    res.status(500).json({ 
      error: 'Error fetching total patients for physician',
      message: error.message 
    });
  }
});

router.get('/total-appointments', [
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const totalAppointments = await physicianDashboardService.getTotalAppointments(organizationId, doctorId);
    res.json({ totalAppointments });
  } catch (error) {
    console.error('Physician total appointments error:', error);
    res.status(500).json({ 
      error: 'Error fetching total appointments for physician',
      message: error.message 
    });
  }
});

router.get('/todays-appointments', [
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const todaysAppointments = await physicianDashboardService.getTodaysAppointments(organizationId, doctorId);
    res.json(todaysAppointments);
  } catch (error) {
    console.error('Physician todays appointments error:', error);
    res.status(500).json({ 
      error: 'Error fetching todays appointments for physician',
      message: error.message 
    });
  }
});

router.get('/patient-queue', [
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const patientQueue = await physicianDashboardService.getPatientQueue(organizationId, doctorId);
    res.json({ patientQueue });
  } catch (error) {
    console.error('Physician patient queue error:', error);
    res.status(500).json({ 
      error: 'Error fetching patient queue for physician',
      message: error.message 
    });
  }
});

router.get('/average-waiting-time', [
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const averageWaitingTime = await physicianDashboardService.getAverageWaitingTime(organizationId, doctorId);
    res.json({ averageWaitingTime });
  } catch (error) {
    console.error('Physician average waiting time error:', error);
    res.status(500).json({ 
      error: 'Error fetching average waiting time for physician',
      message: error.message 
    });
  }
});

router.get('/upcoming-appointments', [
  query('date').optional().isString(),
  query('filter').optional().isString(),
  query('doctorId').optional().isString(),
  query('patientName').optional().isString(),
  query('patientId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const { date, filter, patientSearch } = req.query;
    
    // Use filter as date if provided, otherwise use date parameter
    const filterDate = filter || date;
    
    const upcomingAppointments = await physicianDashboardService.getUpcomingAppointments(
      organizationId, doctorId, filterDate, patientSearch
    );
    // Return array directly for proper Redash table display
    res.json(upcomingAppointments);
  } catch (error) {
    console.error('Physician upcoming appointments error:', error);
    res.status(500).json({ 
      error: 'Error fetching upcoming appointments for physician',
      message: error.message 
    });
  }
});

router.get('/no-show-rate', [
  query('filter').optional().isIn(['7days', '15days', '1month']).withMessage('Filter must be 7days, 15days, or 1month'),
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const filter = req.query.filter || '7days'; // Default to 7 days
    // Backwards compatibility: route kept but now returns attendance rate
    const noShowRate = await physicianDashboardService.getNoShowRate(organizationId, doctorId, filter);
    res.json(noShowRate);
  } catch (error) {
    console.error('Physician no-show rate error:', error);
    res.status(500).json({ 
      error: 'Error fetching no-show rate for physician',
      message: error.message 
    });
  }
});

router.get('/attendance-rate', [
  query('filter').optional().isIn(['7days', '15days', '1month']).withMessage('Filter must be 7days, 15days, or 1month'),
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const filter = req.query.filter || '7days'; // Default to 7 days
    const attendanceRate = await physicianDashboardService.getAttendanceRate(organizationId, doctorId, filter);
    res.json(attendanceRate);
  } catch (error) {
    console.error('Physician attendance rate error:', error);
    res.status(500).json({ 
      error: 'Error fetching attendance rate for physician',
      message: error.message 
    });
  }
});

router.get('/average-consultation-time', [
  query('filter').optional().isIn(['7days', '15days', '1month']).withMessage('Filter must be 7days, 15days, or 1month'),
  query('doctorId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { organizationId, doctorId } = await getUserInfo(req);
    const filter = req.query.filter || '7days'; // Default to 7 days
    const avgConsultationTime = await physicianDashboardService.getAverageConsultationTime(organizationId, doctorId, filter);
    res.json(avgConsultationTime);
  } catch (error) {
    console.error('Physician average consultation time error:', error);
    res.status(500).json({ 
      error: 'Error fetching average consultation time for physician',
      message: error.message 
    });
  }
});

module.exports = router;
