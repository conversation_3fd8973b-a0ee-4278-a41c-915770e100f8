const express = require('express');
const { query, validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const router = express.Router();

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// Generate secure iframe URL with embedded token
router.post('/generate-iframe-url', [
  query('dashboardId').notEmpty().withMessage('Dashboard ID is required'),
  query('organizationId').optional().isString(),
  query('expiresIn').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { dashboardId, organizationId, expiresIn = '1h' } = req.query;
    const userId = req.userId || 'anonymous';

    // Create a secure token for iframe embedding
    const embedToken = jwt.sign(
      {
        userId,
        organizationId: organizationId || req.user?.organizationId,
        dashboardId,
        embedType: 'iframe',
        iat: Math.floor(Date.now() / 1000)
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn }
    );

    // Generate the Redash iframe URL with embedded token
    const redashBaseUrl = process.env.REDASH_URL || 'http://localhost:5000';
    const iframeUrl = `${redashBaseUrl}/embed/dashboard/${dashboardId}?api_key=${embedToken}&hide_parameters=true&hide_header=true`;

    res.json({
      success: true,
      iframeUrl,
      embedToken,
      expiresAt: new Date(Date.now() + (expiresIn.includes('h') ? parseInt(expiresIn) * 3600000 : 3600000)),
      instructions: {
        usage: 'Use this URL directly in an iframe src attribute',
        example: `<iframe src="${iframeUrl}" width="100%" height="400" frameborder="0"></iframe>`
      }
    });

  } catch (error) {
    console.error('Generate iframe URL error:', error);
    res.status(500).json({ 
      error: 'Error generating iframe URL',
      message: error.message 
    });
  }
});

// Generate secure Redash dashboard URL with token
router.post('/generate-dashboard-url', [
  query('dashboardId').notEmpty().withMessage('Dashboard ID is required'),
  query('organizationId').optional().isString(),
  query('expiresIn').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { dashboardId, organizationId, expiresIn = '24h' } = req.query;
    const userId = req.userId || 'anonymous';

    // Create a secure token for dashboard access
    const dashboardToken = jwt.sign(
      {
        userId,
        organizationId: organizationId || req.user?.organizationId,
        dashboardId,
        accessType: 'dashboard',
        iat: Math.floor(Date.now() / 1000)
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn }
    );

    // Generate the Redash dashboard URL with token
    const redashBaseUrl = process.env.REDASH_URL || 'http://localhost:5000';
    const dashboardUrl = `${redashBaseUrl}/dashboard/${dashboardId}?token=${dashboardToken}`;

    res.json({
      success: true,
      dashboardUrl,
      token: dashboardToken,
      expiresAt: new Date(Date.now() + (expiresIn.includes('h') ? parseInt(expiresIn) * 3600000 : 86400000)),
      instructions: {
        usage: 'Use this URL to redirect users to the Redash dashboard',
        note: 'Token is embedded in URL and will auto-authenticate the user'
      }
    });

  } catch (error) {
    console.error('Generate dashboard URL error:', error);
    res.status(500).json({ 
      error: 'Error generating dashboard URL',
      message: error.message 
    });
  }
});

// Generate signed URL for secure access (alternative approach)
router.post('/generate-signed-url', [
  query('dashboardId').notEmpty().withMessage('Dashboard ID is required'),
  query('organizationId').optional().isString(),
  query('accessType').optional().isIn(['dashboard', 'iframe']).withMessage('Access type must be dashboard or iframe'),
  handleValidationErrors
], async (req, res) => {
  try {
    const { dashboardId, organizationId, accessType = 'dashboard' } = req.query;
    const userId = req.userId || 'anonymous';

    // Create signature for URL security
    const timestamp = Date.now();
    const expiresAt = timestamp + (24 * 60 * 60 * 1000); // 24 hours
    
    const signatureData = `${userId}:${organizationId}:${dashboardId}:${expiresAt}`;
    const signature = crypto
      .createHmac('sha256', process.env.JWT_SECRET || 'your-secret-key')
      .update(signatureData)
      .digest('hex');

    const redashBaseUrl = process.env.REDASH_URL || 'http://localhost:5000';
    const baseUrl = accessType === 'iframe' 
      ? `${redashBaseUrl}/embed/dashboard/${dashboardId}`
      : `${redashBaseUrl}/dashboard/${dashboardId}`;

    const signedUrl = `${baseUrl}?user=${userId}&org=${organizationId}&expires=${expiresAt}&sig=${signature}`;

    res.json({
      success: true,
      signedUrl,
      signature,
      expiresAt: new Date(expiresAt),
      accessType,
      instructions: {
        usage: 'Use this cryptographically signed URL for secure access',
        security: 'URL includes expiration time and signature for verification'
      }
    });

  } catch (error) {
    console.error('Generate signed URL error:', error);
    res.status(500).json({ 
      error: 'Error generating signed URL',
      message: error.message 
    });
  }
});

// Validate embedded token (for iframe security)
router.get('/validate-embed-token/:token', async (req, res) => {
  try {
    const { token } = req.params;

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // Check if token is for iframe embedding
    if (decoded.embedType !== 'iframe') {
      return res.status(403).json({
        error: 'Invalid token type',
        message: 'Token is not valid for iframe embedding'
      });
    }

    res.json({
      valid: true,
      tokenData: {
        userId: decoded.userId,
        organizationId: decoded.organizationId,
        dashboardId: decoded.dashboardId,
        embedType: decoded.embedType,
        issuedAt: new Date(decoded.iat * 1000),
        expiresAt: new Date(decoded.exp * 1000)
      }
    });

  } catch (error) {
    res.status(401).json({
      valid: false,
      error: 'Invalid or expired token',
      message: error.message
    });
  }
});

// Get user's accessible dashboards (for dropdown/selection)
router.get('/user-dashboards', async (req, res) => {
  try {
    const userId = req.userId;
    const organizationId = req.user?.organizationId || req.query.organizationId;

    // This is a placeholder - you would implement actual dashboard fetching logic
    // based on your Redash instance and user permissions
    const userDashboards = [
      {
        id: 1,
        name: 'Patient Analytics Dashboard',
        slug: 'patient-analytics',
        description: 'Overview of patient data and trends'
      },
      {
        id: 2,
        name: 'Appointment Metrics',
        slug: 'appointment-metrics',
        description: 'Appointment scheduling and attendance metrics'
      },
      {
        id: 3,
        name: 'Department Performance',
        slug: 'department-performance',
        description: 'Performance metrics by department'
      }
    ];

    res.json({
      success: true,
      dashboards: userDashboards,
      organizationId,
      userId
    });

  } catch (error) {
    console.error('Fetch user dashboards error:', error);
    res.status(500).json({ 
      error: 'Error fetching user dashboards',
      message: error.message 
    });
  }
});

module.exports = router;
