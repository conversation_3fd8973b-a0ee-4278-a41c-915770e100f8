const express = require('express');
const { query, validationResult } = require('express-validator');
const dashboardService = require('../services/dashboardService');
const userService = require('../services/userService');
const router = express.Router();

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

const getOrganizationId = async (req) => {
  if (req.userId) {
    return await userService.getUserOrganization(req.userId);
  } else {
    return req.query.organizationId;
  }
};

router.get('/total-patients', [
  handleValidationErrors
], async (req, res) => {
  try {
    const organizationId = await getOrganizationId(req);
    const totalPatients = await dashboardService.getTotalPatients(organizationId);
    res.json({ totalPatients });
  } catch (error) {
    console.error('Total patients error:', error);
    res.status(500).json({ 
      error: 'Error fetching total patients',
      message: error.message 
    });
  }
});

router.get('/total-appointments', [
  handleValidationErrors
], async (req, res) => {
  try {
    const organizationId = await getOrganizationId(req);
    const totalAppointments = await dashboardService.getTotalAppointments(organizationId);
    res.json({ totalAppointments });
  } catch (error) {
    console.error('Total appointments error:', error);
    res.status(500).json({ 
      error: 'Error fetching total appointments',
      message: error.message 
    });
  }
});

router.get('/patient-queue', [
  handleValidationErrors
], async (req, res) => {
  try {
    const organizationId = await getOrganizationId(req);
    const patientQueue = await dashboardService.getPatientQueue(organizationId);
    res.json({ patientQueue });
  } catch (error) {
    console.error('Patient queue error:', error);
    res.status(500).json({ 
      error: 'Error fetching patient queue',
      message: error.message 
    });
  }
});

router.get('/average-waiting-time', [
  handleValidationErrors
], async (req, res) => {
  try {
    const organizationId = await getOrganizationId(req);
    const averageWaitingTime = await dashboardService.getAverageWaitingTime(organizationId);
    res.json({ averageWaitingTime });
  } catch (error) {
    console.error('Average waiting time error:', error);
    res.status(500).json({ 
      error: 'Error fetching average waiting time',
      message: error.message 
    });
  }
});

router.get('/upcoming-appointments', [
  query('date').optional().isString(),
  query('doctorId').optional().isString(),
  query('patientName').optional().isString(),
  query('patientId').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const organizationId = await getOrganizationId(req);
    const { date, doctorId, patientSearch } = req.query;
    const upcomingAppointments = await dashboardService.getUpcomingAppointments(
      organizationId, date, doctorId, patientSearch
    );
    // Return array directly for proper Redash table display
    res.json(upcomingAppointments);
  } catch (error) {
    console.error('Upcoming appointments error:', error);
    res.status(500).json({ 
      error: 'Error fetching upcoming appointments',
      message: error.message 
    });
  }
});

router.get('/avg-consultation-time-by-department', [
  handleValidationErrors
], async (req, res) => {
  try {
    const organizationId = await getOrganizationId(req);
    const avgConsultationTimeByDepartment = await dashboardService.getAvgConsultationTimeByDepartment(organizationId);
    res.json(avgConsultationTimeByDepartment);
  } catch (error) {
    console.error('Avg consultation time by department error:', error);
    res.status(500).json({ 
      error: 'Error fetching average consultation time by department',
      message: error.message 
    });
  }
});

router.get('/doctors', [
  handleValidationErrors
], async (req, res) => {
  try {
    const organizationId = await getOrganizationId(req);
    const doctors = await dashboardService.getDoctors(organizationId);
    
    res.json(doctors);
  } catch (error) {
    console.error('Doctors error:', error);
    res.status(500).json({ 
      error: 'Error fetching doctors',
      message: error.message 
    });
  }
});

module.exports = router;
