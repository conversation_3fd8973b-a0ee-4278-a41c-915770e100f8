const { CosmosClient } = require('@azure/cosmos');

class CosmosDBClient {
  constructor() {
    this.endpoint = process.env.COSMOS_DB_ENDPOINT;
    this.key = process.env.COSMOS_DB_KEY;
    this.databaseName = process.env.COSMOS_DB_DATABASE;
    
    if (!this.endpoint || !this.key || !this.databaseName) {
      throw new Error('Missing required CosmosDB configuration. Please check your environment variables.');
    }
    
    this.client = new CosmosClient({
      endpoint: this.endpoint,
      key: this.key,
      userAgentSuffix: 'RedashIntegration/1.0.0'
    });
    
    this.database = null;
    this.containers = new Map();
  }

  async initialize() {
    try {
      console.log('🔌 Connecting to CosmosDB...');
      this.database = this.client.database(this.databaseName);
      
      // Test connection
      await this.database.read();
      console.log('✅ CosmosDB connection established successfully');
      
      // Cache commonly used containers
      await this.initializeContainers();
      
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to CosmosDB:', error.message);
      throw error;
    }
  }

  async initializeContainers() {
    try {
      // Get list of containers in the database
      const { resources: containerList } = await this.database.containers.readAll().fetchAll();
      
      console.log('📂 Available containers:', containerList.map(c => c.id).join(', '));
      
      // Cache containers for faster access
      for (const containerInfo of containerList) {
        this.containers.set(containerInfo.id, this.database.container(containerInfo.id));
      }
      
      return containerList.map(c => c.id);
    } catch (error) {
      console.error('Error initializing containers:', error.message);
      return [];
    }
  }

  getContainer(containerName) {
    const container = this.containers.get(containerName);
    if (!container) {
      // If container not cached, create and cache it
      const newContainer = this.database.container(containerName);
      this.containers.set(containerName, newContainer);
      return newContainer;
    }
    return container;
  }

  async queryContainer(containerName, query, parameters = []) {
    try {
      console.log(`🔍 Querying container: ${containerName}`);
      
      const container = this.getContainer(containerName);
      
      const querySpec = {
        query: query,
        parameters: parameters
      };
      
      const { resources } = await container.items.query(querySpec).fetchAll();
      console.log(`✅ Query successful, returned ${resources.length} results`);
      return resources;
    } catch (error) {
      console.error(`❌ Error querying container ${containerName}:`, error.message);
      if (error.code) {
        console.error(`Error code: ${error.code}`);
      }
      throw error;
    }
  }

  async getContainerStats(containerName) {
    try {
      const container = this.getContainer(containerName);
      
      // Get document count
      const countQuery = "SELECT VALUE COUNT(1) FROM c";
      const { resources: countResult } = await container.items.query(countQuery).fetchAll();
      const documentCount = countResult[0] || 0;
      
      return {
        containerName,
        documentCount,
        lastQueried: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error getting stats for container ${containerName}:`, error.message);
      return {
        containerName,
        documentCount: 0,
        error: error.message
      };
    }
  }

  async getPatients(filters = {}) {
    const { limit = 100, offset = 0, organizationId, status } = filters;
    
    let query = "SELECT * FROM c";
    const parameters = [];
    
    if (organizationId) {
      query += " AND c.organizationId = @organizationId";
      parameters.push({ name: "@organizationId", value: organizationId });
    }
    
    if (status) {
      query += " AND c.status = @status";
      parameters.push({ name: "@status", value: status });
    }
    
    query += " ORDER BY c._ts DESC";
    query += " OFFSET @offset LIMIT @limit";
    
    parameters.push(
      { name: "@offset", value: offset },
      { name: "@limit", value: limit }
    );
    
    return await this.queryContainer('Patient', query, parameters);
  }

  async getAppointments(filters = {}) {
    const { 
      limit = 100, 
      offset = 0, 
      startDate, 
      endDate, 
      status, 
      organizationId 
    } = filters;
    
    let query = "SELECT * FROM c WHERE c.type = 'appointment'";
    const parameters = [];
    
    if (startDate) {
      query += " AND c.appointmentDate >= @startDate";
      parameters.push({ name: "@startDate", value: startDate });
    }
    
    if (endDate) {
      query += " AND c.appointmentDate <= @endDate";
      parameters.push({ name: "@endDate", value: endDate });
    }
    
    if (status) {
      query += " AND c.status = @status";
      parameters.push({ name: "@status", value: status });
    }
    
    if (organizationId) {
      query += " AND c.organizationId = @organizationId";
      parameters.push({ name: "@organizationId", value: organizationId });
    }
    
    query += " ORDER BY c.appointmentDate ASC";
    query += " OFFSET @offset LIMIT @limit";
    
    parameters.push(
      { name: "@offset", value: offset },
      { name: "@limit", value: limit }
    );
    
    return await this.queryContainer('appointments', query, parameters);
  }

  async dispose() {
    try {
      if (this.client) {
        await this.client.dispose();
        console.log('CosmosDB client disposed');
      }
    } catch (error) {
      console.error('Error disposing CosmosDB client:', error.message);
    }
  }
}

// Create singleton instance
const cosmosDbClient = new CosmosDBClient();

// Initialize connection
cosmosDbClient.initialize().catch(error => {
  console.error('Failed to initialize CosmosDB client:', error.message);
});

module.exports = cosmosDbClient;
