const dashboardQueries = require('../queries/dashboardQueries');
const cosmosDbClient = require('../config/cosmosdb');

module.exports = {
  async getTotalPatients(organizationId) {
    const result = await dashboardQueries.getTotalPatients(cosmosDbClient, organizationId);
    return Array.isArray(result) && result.length > 0 ? result[0] : 0;
  },
  async getTotalAppointments(organizationId) {
    const result = await dashboardQueries.getTotalAppointments(cosmosDbClient, organizationId);
    return Array.isArray(result) && result.length > 0 ? result[0] : 0;
  },
  async getPatientQueue(organizationId) {
    const result = await dashboardQueries.getPatientQueue(cosmosDbClient, organizationId);
    return Array.isArray(result) && result.length > 0 ? result[0] : 0;
  },
  async getAverageWaitingTime(organizationId) {
    return await dashboardQueries.getAverageWaitingTime(cosmosDbClient, organizationId);
  },
  async getUpcomingAppointments(organizationId, date, doctorId, patientSearch) {
    const result = await dashboardQueries.getUpcomingAppointments(cosmosDbClient, organizationId, date, doctorId, patientSearch);
    return Array.isArray(result) ? result : [];
  },
  async getAvgConsultationTimeByDepartment(organizationId) {
    return await dashboardQueries.getAvgConsultationTimeByDepartment(cosmosDbClient, organizationId);
  },
  async getDoctors(organizationId) {
    const result = await dashboardQueries.getDoctors(cosmosDbClient, organizationId);
    return Array.isArray(result) ? result : [];
  }
};
