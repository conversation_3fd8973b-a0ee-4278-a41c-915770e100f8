const physicianDashboardQueries = require('../queries/physicianDashboardQueries');
const cosmosDbClient = require('../config/cosmosdb');

module.exports = {
  async getTotalPatients(organizationId, doctorId) {
    const result = await physicianDashboardQueries.getTotalPatients(cosmosDbClient, organizationId, doctorId);
    return Array.isArray(result) && result.length > 0 ? result[0] : 0;
  },
  
  async getTotalAppointments(organizationId, doctorId) {
    const result = await physicianDashboardQueries.getTotalAppointments(cosmosDbClient, organizationId, doctorId);
    return Array.isArray(result) && result.length > 0 ? result[0] : 0;
  },
  
  async getTodaysAppointments(organizationId, doctorId) {
    return await physicianDashboardQueries.getTodaysAppointments(cosmosDbClient, organizationId, doctorId);
  },
  
  async getPatientQueue(organizationId, doctorId) {
    const result = await physicianDashboardQueries.getPatientQueue(cosmosDbClient, organizationId, doctorId);
    return Array.isArray(result) && result.length > 0 ? result[0] : 0;
  },
  
  async getAverageWaitingTime(organizationId, doctorId) {
    return await physicianDashboardQueries.getAverageWaitingTime(cosmosDbClient, organizationId, doctorId);
  },
  
  async getUpcomingAppointments(organizationId, doctorId, date, patientSearch) {
    const result = await physicianDashboardQueries.getUpcomingAppointments(cosmosDbClient, organizationId, doctorId, date, patientSearch);
    return Array.isArray(result) ? result : [];
  },
  
  async getNoShowRate(organizationId, doctorId, period) {
    return await physicianDashboardQueries.getNoShowRate(cosmosDbClient, organizationId, doctorId, period);
  },
  
  async getAverageConsultationTime(organizationId, doctorId, filter) {
    return await physicianDashboardQueries.getAverageConsultationTime(cosmosDbClient, organizationId, doctorId, filter);
  },
  
  async getAttendanceRate(organizationId, doctorId, filter) {
    return await physicianDashboardQueries.getAttendanceRate(cosmosDbClient, organizationId, doctorId, filter);
  }
};
