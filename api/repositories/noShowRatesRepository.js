const cosmosDbClient = require('../config/cosmosdb');
const { NO_SHOW_QUERY } = require('../queries/noShowRatesQuery');

async function getNoShowRatesRaw({ organizationId, startDate }) {
  let baseCondition = `c.created_on >= '${startDate.toISOString()}'`;
  if (organizationId) {
    baseCondition += ` AND c.organizationId = '${organizationId}'`;
  }
  const noShowQuery = NO_SHOW_QUERY(baseCondition);
  return await cosmosDbClient.queryContainer('Queues', noShowQuery);
}

module.exports = { getNoShowRatesRaw };
