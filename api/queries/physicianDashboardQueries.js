module.exports = {
  async getTotalPatients(db, organizationId, doctorId) {
    let query = `
      SELECT DISTINCT c.patientId
      FROM c 
      WHERE IS_DEFINED(c.patientId)
        AND IS_DEFINED(c.doctorId)
        AND c.doctorId = '${doctorId}'
        AND c.patientId != null
        AND c.patientId != ''
    `;
    
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`;
    }
    
    try {
      const result = await db.queryContainer('Queues', query);
      const uniquePatients = result ? result.length : 0;
      return [uniquePatients];
    } catch (error) {
      console.error('Error in getTotalPatients:', error);
      throw error;
    }
  },

  async getTotalAppointments(db, organizationId, doctorId) {
    const todayString = new Date().toISOString().split('T')[0];
    
    try {
      let query;
      if (organizationId) {
        query = `SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = '${organizationId}' AND c.doctorId = '${doctorId}' AND STARTSWITH(c.date, '${todayString}')`;
      } else {
        query = `SELECT VALUE COUNT(1) FROM c WHERE c.doctorId = '${doctorId}' AND STARTSWITH(c.date, '${todayString}')`;
      }
      
      const result = await db.queryContainer('Queues', query);
      return result;
    } catch (error) {
      console.error('Error in getTotalAppointments:', error);
      throw error;
    }
  },

  async getTodaysAppointments(db, organizationId, doctorId) {
    const todayString = new Date().toISOString().split('T')[0];
    let query = `
      SELECT 
        c.patientId,
        c.time,
        c.status,
        c.date
      FROM c 
      WHERE IS_DEFINED(c.doctorId)
        AND c.doctorId = '${doctorId}'
        AND STARTSWITH(c.date, '${todayString}')
    `;
    
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`;
    }
    
    console.log('🔍 Getting today\'s appointments for doctor:', doctorId);
    const appointments = await db.queryContainer('Queues', query);
    
    // Calculate counts
    const totalCount = appointments.length;
    let pendingCount = 0;
    let completedCount = 0;
    
    // Enrich with patient names and calculate counts
    const enrichedAppointments = [];
    
    for (const appointment of appointments) {
      let patientName = 'Unknown Patient';
      
      // Get patient name
      if (appointment.patientId) {
        try {
          let patientQuery = `SELECT * FROM p WHERE p.id = '${appointment.patientId}'`;
          let patients = await db.queryContainer('PatientProfiles', patientQuery);
          
          if (patients.length > 0) {
            const patient = patients[0];
            patientName = patient.name || patient.fullName || patient.patientName || 'Unknown Patient';
          }
        } catch (error) {
          // Patient name lookup failed, keep default
        }
      }
      
      const status = (appointment.status || '').trim().toLowerCase();
      if (status === 'consultation-done') {
        completedCount++;
      } else if (status === 'booked-booked' || status === 'booked-arrived' || 
                 status === 'consultation-arrived' || status === 'booked-noshow') {
        pendingCount++;
      }
      
      // Return only the required fields in the specified order: patient, time, status
      enrichedAppointments.push({
        patient: patientName,
        time: appointment.time || '',
        status: appointment.status || ''
      });
    }
    
    return {
      totalCount,
      pendingCount,
      completedCount
    };
  },

  async getPatientQueue(db, organizationId, doctorId) {
    const todayString = new Date().toISOString().split('T')[0];
    let query = `
      SELECT VALUE COUNT(1) 
      FROM c 
      WHERE IS_DEFINED(c.doctorId)
        AND c.doctorId = '${doctorId}'
        AND (STARTSWITH(c.status, "Booked") OR c.status = "Booked-Arrived" OR c.status = "Booked-Booked") 
        AND STARTSWITH(c.date, '${todayString}')
    `;
    
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`;
    }
    
    return await db.queryContainer('Queues', query);
  },

  async getAverageWaitingTime(db, organizationId, doctorId) {
    const todayString = new Date().toISOString().split('T')[0];
    let query = `
      SELECT c.patientArrivalTime, c.consultationStartTime, c.consultationEndTime 
      FROM c 
      WHERE IS_DEFINED(c.doctorId)
        AND c.doctorId = '${doctorId}'
        AND STARTSWITH(c.date, '${todayString}') 
        AND IS_DEFINED(c.consultationStartTime) 
        AND IS_DEFINED(c.patientArrivalTime)
    `;
    
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`;
    }
    
    const results = await db.queryContainer('Queues', query);
    if (!results || results.length === 0) return 0;
    
    const waitingTimes = results.map(record => {
      const arrival = new Date(record.patientArrivalTime);
      const consultation = new Date(record.consultationStartTime);
      return consultation.getTime() - arrival.getTime();
    }).filter(time => time > 0);
    
    if (waitingTimes.length === 0) return 0;
    
    const avgWaitingTime = waitingTimes.reduce((sum, time) => sum + time, 0) / waitingTimes.length;
    return Math.round(avgWaitingTime / (1000 * 60)); // Convert to minutes
  },

  async getUpcomingAppointments(db, organizationId, doctorId, date, patientSearch) {
    let query = `
      SELECT 
        c.patientId,
        c.time,
        c.status,
        c.date
      FROM c 
      WHERE IS_DEFINED(c.status)
        AND IS_DEFINED(c.doctorId)
        AND c.doctorId = '${doctorId}'
        AND NOT ARRAY_CONTAINS(['canceled', 'consultation-done'], LOWER(c.status))
    `;

    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`;
    }

    // Default to today's appointments only
    const today = new Date().toISOString().split('T')[0];
    const filterDate = date || today;
    console.log('🔍 Date filter applied for doctor', doctorId, ':', filterDate);
    
    const dateStr = filterDate.includes('T') ? filterDate.split('T')[0] : filterDate;
    query += ` AND (STARTSWITH(c.date, '${dateStr}') OR CONTAINS(c.date, '${dateStr}'))`;

    // Patient search - support both patient name and patient ID
    if (patientSearch) {
      query += ` AND (c.patientId = '${patientSearch}' OR CONTAINS(UPPER(c.patientId), UPPER('${patientSearch}')))`;
    }
    
    console.log('🔍 Getting upcoming appointments for doctor:', doctorId, 'Query:', query);
    const appointments = await db.queryContainer('Queues', query);
    console.log('🔍 Found appointments for doctor:', appointments.length);

    // Enrich with patient names and return only required fields
    const enrichedAppointments = [];

    for (const appointment of appointments) {
      let patientName = 'Unknown Patient';
      
      // Get patient name
      if (appointment.patientId) {
        try {
          let debugQuery = `SELECT * FROM p WHERE p.id = '${appointment.patientId}'`;
          let debugPatients = await db.queryContainer('PatientProfiles', debugQuery);
          
          if (debugPatients.length > 0) {
            const patient = debugPatients[0];
            patientName = patient.name || patient.fullName || patient.patientName || 'Unknown Patient';
          }
        } catch (error) {
          console.log('Error fetching patient name for', appointment.patientId, ':', error.message);
        }
      }

      // Apply patient name search filter if provided
      if (patientSearch && patientName !== 'Unknown Patient') {
        const searchTerm = patientSearch.toLowerCase();
        if (!patientName.toLowerCase().includes(searchTerm) && 
            !appointment.patientId.toLowerCase().includes(searchTerm)) {
          continue; // Skip this appointment if it doesn't match search
        }
      }

      // Return only the required fields in order: patient, time, status
      enrichedAppointments.push({
        patient: patientName,
        time: appointment.time || '',
        status: appointment.status || ''
      });
    }

    console.log('🔍 Enriched appointments for doctor:', enrichedAppointments.length);
    return enrichedAppointments;
  },

  async getAttendanceRate(db, organizationId, doctorId, filter) {
    const endDate = new Date();
    const startDate = new Date();

    // Convert filter to number of days
    let days;
    switch(filter) {
      case '7days':
        days = 7;
        break;
      case '15days':
        days = 15;
        break;
      case '1month':
        days = 30;
        break;
      default:
        days = 7; // default to 7 days
    }

    startDate.setDate(startDate.getDate() - days);

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    console.log('🔍 Getting attendance rate for doctor:', doctorId, 'Filter:', filter, '(' + days + ' days)');

    // Get all appointments in the period for this doctor
    let appointmentsQuery = `
      SELECT c.date, c.status, c.patientId
      FROM c 
      WHERE IS_DEFINED(c.doctorId)
        AND c.doctorId = '${doctorId}'
        AND c.date >= '${startDateStr}'
        AND c.date <= '${endDateStr}'
    `;

    if (organizationId) {
      appointmentsQuery += ` AND c.organizationId = '${organizationId}'`;
    }

    const appointments = await db.queryContainer('Queues', appointmentsQuery);

    // Define attended statuses (exact matches and common variants)
    const attendedKeywords = [
      'arrived',
      'done',
      'consultation-arrived',
      'consultation-done',
      'done-arrived',
      'booked-arrived'
    ];

    // Group appointments by date and calculate attendance
    const dateStats = {};

    appointments.forEach(appointment => {
      const date = appointment.date.includes('T') ? appointment.date.split('T')[0] : appointment.date;
      if (!dateStats[date]) {
        dateStats[date] = { total: 0, attended: 0 };
      }

      dateStats[date].total++;

      const status = (appointment.status || '').trim().toLowerCase();
      // Consider attended when any of the attended keywords are present or exact
      if (attendedKeywords.some(k => status === k || status.includes(k))) {
        dateStats[date].attended++;
      }
    });

    // Generate all dates in the range and fill with zeros if no appointments
    const result = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const dayNumber = parseInt(dateStr.split('-')[2], 10);

      const stats = dateStats[dateStr] || { total: 0, attended: 0 };
      const attendanceRate = stats.total > 0 ? (stats.attended / stats.total * 100).toFixed(2) : 0;

      result.push({
        date: dayNumber,
        totalAppointments: stats.total,
        attended: stats.attended,
        attendanceRate: parseFloat(attendanceRate),
        fullDate: dateStr // Keep full date for proper sorting
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Sort by full date to ensure proper chronological order, then remove fullDate
    result.sort((a, b) => a.fullDate.localeCompare(b.fullDate));

    // Remove the fullDate property before returning
    const finalResult = result.map(({ fullDate, ...rest }) => rest);

    console.log('🔍 Attendance rate results for doctor:', finalResult.length, 'days');
    return finalResult;
  },

  async getAverageConsultationTime(db, organizationId, doctorId, filter) {
    const endDate = new Date();
    const startDate = new Date();
    
    // Convert filter to number of days
    let days;
    switch(filter) {
      case '7days':
        days = 7;
        break;
      case '15days':
        days = 15;
        break;
      case '1month':
        days = 30;
        break;
      default:
        days = 7; // default to 7 days
    }
    
    startDate.setDate(startDate.getDate() - days);
    
    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];
    
    console.log('🔍 Getting average consultation time for doctor:', doctorId, 'Filter:', filter, '(' + days + ' days)');
    
    // Get all appointments with consultation times in the period for this doctor
    let appointmentsQuery = `
      SELECT c.date, c.consultationStartTime, c.consultationEndTime
      FROM c 
      WHERE IS_DEFINED(c.doctorId)
        AND c.doctorId = '${doctorId}'
        AND c.date >= '${startDateStr}'
        AND c.date <= '${endDateStr}'
        AND IS_DEFINED(c.consultationStartTime)
        AND IS_DEFINED(c.consultationEndTime)
        AND c.consultationStartTime != null
        AND c.consultationEndTime != null
    `;
    
    if (organizationId) {
      appointmentsQuery += ` AND c.organizationId = '${organizationId}'`;
    }
    
    const appointments = await db.queryContainer('Queues', appointmentsQuery);
    
    // Group appointments by date and calculate average consultation time
    const dateStats = {};
    
    appointments.forEach(appointment => {
      const date = appointment.date.includes('T') ? appointment.date.split('T')[0] : appointment.date;
      if (!dateStats[date]) {
        dateStats[date] = { consultationTimes: [] };
      }
      
      try {
        const startTime = new Date(appointment.consultationStartTime);
        const endTime = new Date(appointment.consultationEndTime);
        const consultationTimeMinutes = (endTime - startTime) / (1000 * 60); // Convert to minutes
        
        if (consultationTimeMinutes > 0 && consultationTimeMinutes < 480) { // Reasonable limit: less than 8 hours
          dateStats[date].consultationTimes.push(consultationTimeMinutes);
        }
      } catch (error) {
        console.log('Error calculating consultation time for appointment:', error.message);
      }
    });
    
    // Generate all dates in the range and fill with zeros if no appointments
    const result = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const dayNumber = parseInt(dateStr.split('-')[2], 10);
      
      const stats = dateStats[dateStr];
      let avgConsultationTime = 0;
      
      if (stats && stats.consultationTimes.length > 0) {
        const totalTime = stats.consultationTimes.reduce((sum, time) => sum + time, 0);
        avgConsultationTime = parseFloat((totalTime / stats.consultationTimes.length).toFixed(2));
      }
      
      result.push({
        date: dayNumber,
        avgConsultationTime: avgConsultationTime,
        appointmentsCount: stats ? stats.consultationTimes.length : 0,
        fullDate: dateStr // Keep full date for proper sorting
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // Sort by full date to ensure proper chronological order, then remove fullDate
    result.sort((a, b) => a.fullDate.localeCompare(b.fullDate));
    
    // Remove the fullDate property before returning
    const finalResult = result.map(({ fullDate, ...rest }) => rest);
    
    console.log('🔍 Average consultation time results for doctor:', finalResult.length, 'days');
    return finalResult;
  }
};
