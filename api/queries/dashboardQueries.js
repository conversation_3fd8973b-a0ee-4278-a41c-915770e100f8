module.exports = {
  async getTotalPatients(db, organizationId) {
    let query;
    if (organizationId) {
      query = `SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = '${organizationId}'`;
    } else {
      query = `SELECT VALUE COUNT(1) FROM c`;
    }
    return await db.queryContainer('PatientProfiles', query);
  },

  async getTotalAppointments(db, organizationId) {
    const todayString = new Date().toISOString().split('T')[0];
    let query;
    if (organizationId) {
      query = `SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = '${organizationId}' AND STARTSWITH(c.date, '${todayString}')`;
    } else {
      query = `SELECT VALUE COUNT(1) FROM c WHERE STARTSWITH(c.date, '${todayString}')`;
    }
    return await db.queryContainer('Queues', query);
  },

  async getPatientQueue(db, organizationId) {
    const todayString = new Date().toISOString().split('T')[0];
    let query;
    if (organizationId) {
      query = `SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = '${organizationId}' AND (STARTSWITH(c.status, "Booked") OR c.status = "Booked-Arrived" OR c.status = "Booked-Booked") AND STARTSWITH(c.date, '${todayString}')`;
    } else {
      query = `SELECT VALUE COUNT(1) FROM c WHERE (STARTSWITH(c.status, "Booked") OR c.status = "Booked-Arrived" OR c.status = "Booked-Booked") AND STARTSWITH(c.date, '${todayString}')`;
    }
    return await db.queryContainer('Queues', query);
  },

  async getAverageWaitingTime(db, organizationId) {
    const todayString = new Date().toISOString().split('T')[0];
    let query;
    if (organizationId) {
      query = `SELECT c.patientArrivalTime, c.consultationStartTime, c.consultationEndTime FROM c WHERE c.organizationId = '${organizationId}' AND STARTSWITH(c.date, '${todayString}') AND IS_DEFINED(c.consultationStartTime) AND IS_DEFINED(c.patientArrivalTime)`;
    } else {
      query = `SELECT c.patientArrivalTime, c.consultationStartTime, c.consultationEndTime FROM c WHERE STARTSWITH(c.date, '${todayString}') AND IS_DEFINED(c.consultationStartTime) AND IS_DEFINED(c.patientArrivalTime)`;
    }
    
    const results = await db.queryContainer('Queues', query);
    
    // Calculate average waiting time from results
    if (!results || results.length === 0) return 0;
    
    const waitingTimes = results.map(record => {
      const arrival = new Date(record.patientArrivalTime);
      const consultation = new Date(record.consultationStartTime);
      return (consultation - arrival) / (1000 * 60); // minutes
    });
    
    const average = waitingTimes.reduce((sum, time) => sum + time, 0) / waitingTimes.length;
    return Math.round(average);
  },

  async getUpcomingAppointments(db, organizationId, date, doctorId, patientSearch) {
    let query = `
      SELECT 
        c.patientId,
        c.doctorId,
        c.time,
        c.status,
        c.date
      FROM c 
      WHERE IS_DEFINED(c.status)
        AND NOT ARRAY_CONTAINS(['canceled', 'consultation-done'], LOWER(c.status))
    `;

    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`;
    }

    // Default to today's appointments only
    const today = new Date().toISOString().split('T')[0];
    const filterDate = date || today;
    const dateStr = filterDate.includes('T') ? filterDate.split('T')[0] : filterDate;
    query += ` AND (STARTSWITH(c.date, '${dateStr}') OR CONTAINS(c.date, '${dateStr}'))`;
    
    if (doctorId) {
      query += ` AND c.doctorId = '${doctorId}'`;
    }

    if (patientSearch) {
      query += ` AND (c.patientId = '${patientSearch}' OR CONTAINS(UPPER(c.patientId), UPPER('${patientSearch}')))`;
    }
    
    const appointments = await db.queryContainer('Queues', query);

    // Enrich with patient names and return only required fields
    const enrichedAppointments = [];

    for (const appointment of appointments) {
      let patientName = 'Unknown Patient';
      
      // Get patient name - debug and try different query approaches
      if (appointment.patientId) {
        try {
          // First, let's see the full patient record structure
          let debugQuery = `SELECT * FROM p WHERE p.id = '${appointment.patientId}'`;
          let debugPatients = await db.queryContainer('PatientProfiles', debugQuery);
          
          if (debugPatients.length > 0) {
            const patient = debugPatients[0];

            
            // Try different possible field paths
            if (patient.general && patient.general.name) {
              patientName = patient.general.name;
            } else if (patient.name) {
              patientName = patient.name;
            } else if (patient.general && patient.general.firstName) {
              patientName = `${patient.general.firstName} ${patient.general.lastName || ''}`.trim();
            } else if (patient.firstName) {
              patientName = `${patient.firstName} ${patient.lastName || ''}`.trim();
            } else if (patient.fullName) {
              patientName = patient.fullName;
            } else if (patient.patientName) {
              patientName = patient.patientName;
            } else {
              // Check all top-level string fields that might be the name
              const stringFields = Object.keys(patient).filter(key => 
                typeof patient[key] === 'string' && 
                key.toLowerCase().includes('name') &&
                patient[key].length > 1
              );
              if (stringFields.length > 0) {
                patientName = patient[stringFields[0]];
              }
            }
          }
          
          // Debug patient name resolution

          
        } catch (error) {
          console.log('Error fetching patient name for', appointment.patientId, ':', error.message);
        }
      }

      // Apply patient name search filter if provided
      if (patientSearch && patientName !== 'Unknown Patient') {
        const searchTerm = patientSearch.toLowerCase();
        if (!patientName.toLowerCase().includes(searchTerm) && 
            !appointment.patientId.toLowerCase().includes(searchTerm)) {
          continue; // Skip this appointment if it doesn't match search
        }
      }

      // Return only the required fields in requested order: patient, time, status
      enrichedAppointments.push({
        patient: patientName,
        time: appointment.time || '',
        status: appointment.status || ''
      });
    }


    return enrichedAppointments;
  },

  async getAvgConsultationTimeByDepartment(db, organizationId) {
    // Get departments from Doctors container
    let doctorQuery = `
      SELECT DISTINCT d.general.department as department
      FROM d 
      WHERE IS_DEFINED(d.general.department)
        AND d.general.department != null
        AND d.general.department != ""
    `;

    if (organizationId) {
      doctorQuery += ` AND d.organizationId = '${organizationId}'`;
    }

    const doctors = await db.queryContainer('Doctors', doctorQuery);
    
    if (doctors.length === 0) {
      return [];
    }
    
    // Initialize all departments with zero
    const departmentData = {};
    const seenDepartments = new Set();
    
    doctors.forEach(doctor => {
      if (doctor.department && doctor.department.trim()) {
        const deptName = doctor.department.trim();
        
        if (!seenDepartments.has(deptName)) {
          seenDepartments.add(deptName);
          departmentData[deptName] = {
            department: deptName,
            totalTime: 0,
            count: 0
          };
        }
      }
    });
    
    // Get consultation times from Queues container
    try {
      let queueConsultationQuery = `
        SELECT 
          q.consultationStartTime,
          q.consultationEndTime,
          q.doctorId
        FROM q 
        WHERE IS_DEFINED(q.consultationStartTime) 
          AND IS_DEFINED(q.consultationEndTime)
          AND IS_DEFINED(q.doctorId)
      `;

      if (organizationId) {
        queueConsultationQuery += ` AND q.organizationId = '${organizationId}'`;
      }

      const queueConsultations = await db.queryContainer('Queues', queueConsultationQuery);
      
      if (queueConsultations.length > 0) {
        // Get doctor to department mappings
        let doctorUserLinkQuery = `
          SELECT d.id, d.userId, d.general.department as department
          FROM d 
          WHERE IS_DEFINED(d.general.department)
        `;

        if (organizationId) {
          doctorUserLinkQuery += ` AND d.organizationId = '${organizationId}'`;
        }

        const doctorUserLinks = await db.queryContainer('Doctors', doctorUserLinkQuery);
        const doctorToDept = {};
        
        // Map via userId and doctor ID
        doctorUserLinks.forEach(doctor => {
          if (doctor.userId && doctor.department) {
            doctorToDept[doctor.userId] = doctor.department;
          }
          if (doctor.id && doctor.department) {
            doctorToDept[doctor.id] = doctor.department;
          }
        });

        // Get additional doctor mappings
        let doctorDeptQuery = `
          SELECT d.id, d.general.department as department
          FROM d 
          WHERE IS_DEFINED(d.general.department)
        `;

        if (organizationId) {
          doctorDeptQuery += ` AND d.organizationId = '${organizationId}'`;
        }

        const doctorMappings = await db.queryContainer('Doctors', doctorDeptQuery);
        doctorMappings.forEach(doc => {
          if (doc.department) {
            doctorToDept[doc.id] = doc.department;
          }
        });
        
        // Process consultations that don't have department mappings
        for (const consultation of queueConsultations) {
          if (!doctorToDept[consultation.doctorId]) {
            try {
              const userLookup = await db.queryContainer('Users', 
                `SELECT u.email, u.name FROM u WHERE u.id = '${consultation.doctorId}'`
              );
              
              if (userLookup.length > 0) {
                const userEmail = userLookup[0].email;
                const userName = userLookup[0].name;
                
                if (userEmail) {
                  const doctorByEmail = await db.queryContainer('Doctors', 
                    `SELECT d.id, d.general.department FROM d 
                     WHERE d.username = '${userEmail}' OR d.email = '${userEmail}'`
                  );
                  
                  if (doctorByEmail.length > 0 && doctorByEmail[0].department) {
                    doctorToDept[consultation.doctorId] = doctorByEmail[0].department;
                  }
                }
                
                if (!doctorToDept[consultation.doctorId] && userName) {
                  const doctorByName = await db.queryContainer('Doctors', 
                    `SELECT d.id, d.general.department FROM d 
                     WHERE d.general.name = '${userName}' OR d.name = '${userName}'`
                  );
                  
                  if (doctorByName.length === 1 && doctorByName[0].department) {
                    doctorToDept[consultation.doctorId] = doctorByName[0].department;
                  }
                }
              }
            } catch (err) {
              // Continue processing other consultations
            }
          }
        }
        
        // Calculate consultation times
        queueConsultations.forEach(consultation => {
          const department = doctorToDept[consultation.doctorId];
          
          if (department && departmentData[department]) {
            const start = new Date(consultation.consultationStartTime);
            const end = new Date(consultation.consultationEndTime);
            const consultationTime = (end - start) / (1000 * 60); // minutes
            
            if (consultationTime > 0 && consultationTime < 300) { // Valid time (less than 5 hours)
              departmentData[department].totalTime += consultationTime;
              departmentData[department].count += 1;
            }
          }
        });
      }
    } catch (error) {
      // Continue with zero values for all departments
    }
    
    // Convert to final format - include ALL departments with avgConsultationTime as 0 if no data
    const departmentMap = new Map();
    
    // Add all found departments
    Object.values(departmentData).forEach(dept => {
      if (dept.department && dept.department.trim()) {
        const deptName = dept.department.trim();
        
        if (!departmentMap.has(deptName)) {
          departmentMap.set(deptName, {
            department: deptName,
            totalTime: 0,
            count: 0
          });
        }
        
        const consolidated = departmentMap.get(deptName);
        consolidated.totalTime += dept.totalTime;
        consolidated.count += dept.count;
      }
    });
    
    // Convert to final format - show ALL departments (including those with 0 consultation time)
    const chartData = Array.from(departmentMap.values())
      .map(dept => ({
        department: dept.department,
        avgConsultationTime: dept.count > 0 ? 
          Math.round((dept.totalTime / dept.count) * 100) / 100 : 0
      }))
      .filter(item => item.department && item.department.length > 0)
      .sort((a, b) => {
        // Sort by consultation time descending, then by name
        if (a.avgConsultationTime !== b.avgConsultationTime) {
          return b.avgConsultationTime - a.avgConsultationTime;
        }
        return a.department.localeCompare(b.department);
      });
    
    return chartData;
  },

  async getDoctors(db, organizationId) {
    console.log('🔍 Getting doctors with role doctor...');
    console.log('🔍 OrganizationId:', organizationId);
    
    // First, let's see what's actually in the Doctors container
    console.log('🔍 DEBUG: Checking Doctors container structure...');
    const debugQuery = `SELECT TOP 5 * FROM d`;
    const debugResults = await db.queryContainer('Doctors', debugQuery);
    console.log('🔍 DEBUG: Sample Doctors container documents:', JSON.stringify(debugResults, null, 2));
    
    // Check for role field variations
    const roleCheckQuery = `SELECT DISTINCT d.role FROM d WHERE IS_DEFINED(d.role)`;
    const roleResults = await db.queryContainer('Doctors', roleCheckQuery);
    console.log('🔍 DEBUG: All roles in Doctors container:', roleResults);
    
    // Based on the actual data structure, doctors don't have a 'role' field
    // They are in the Doctors container, so we can assume they are doctors
    let query = `
      SELECT 
        d.id,
        d.name,
        d.doctorfirstname,
        d.doctorlastname,
        d.doctormiddlename
      FROM d
    `;

    if (organizationId) {
      query += ` AND d.organizationId = '${organizationId}'`;
    }

    console.log('🔍 Getting doctors with query:', query);
    const doctors = await db.queryContainer('Doctors', query);
    console.log('🔍 Found doctors:', doctors.length);
    console.log('🔍 Doctors result:', JSON.stringify(doctors, null, 2));

    // Also try Users container for users with doctor userType
    let userQuery = `
      SELECT 
        u.id,
        u.displayName,
        u.givenName,
        u.surname,
        u.firstname,
        u.lastname
      FROM u 
      WHERE (u.userType = 'doctor' OR LOWER(u.userType) = 'doctor' OR CONTAINS(LOWER(u.userType), 'doctor') OR
             u.userRole = 'doctor' OR LOWER(u.userRole) = 'doctor' OR CONTAINS(LOWER(u.userRole), 'doctor') OR
             CONTAINS(LOWER(u.jobTitle), 'doctor') OR CONTAINS(LOWER(u.jobTitle), 'physician'))
    `;

    if (organizationId) {
      userQuery += ` AND u.organizationId = '${organizationId}'`;
    }

    console.log('🔍 Getting doctor users with query:', userQuery);
    
    // First, let's see what's actually in the Users container
    console.log('🔍 DEBUG: Checking Users container structure...');
    const userDebugQuery = `SELECT TOP 5 * FROM u`;
    const userDebugResults = await db.queryContainer('Users', userDebugQuery);
    console.log('🔍 DEBUG: Sample Users container documents:', JSON.stringify(userDebugResults, null, 2));
    
    // Check for role field variations in Users
    const userRoleCheckQuery = `SELECT DISTINCT u.role FROM u WHERE IS_DEFINED(u.role)`;
    const userRoleResults = await db.queryContainer('Users', userRoleCheckQuery);
    console.log('🔍 DEBUG: All roles in Users container:', userRoleResults);
    
    const doctorUsers = await db.queryContainer('Users', userQuery);
    console.log('🔍 Found doctor users:', doctorUsers.length);
    console.log('🔍 Doctor users result:', JSON.stringify(doctorUsers, null, 2));

    // Combine and normalize doctor data
    const allDoctors = [];

    // Process doctors from Doctors container
    for (const doctor of doctors) {
      let doctorName = 'Unknown Doctor';
      
      if (doctor.name) {
        doctorName = doctor.name;
      } else if (doctor.doctorfirstname) {
        const firstName = doctor.doctorfirstname || '';
        const middleName = doctor.doctormiddlename ? ` ${doctor.doctormiddlename}` : '';
        const lastName = doctor.doctorlastname ? ` ${doctor.doctorlastname}` : '';
        doctorName = `${firstName}${middleName}${lastName}`.trim();
      } else if (doctor.firstName) {
        doctorName = `${doctor.firstName} ${doctor.lastName || ''}`.trim();
      }

      allDoctors.push({
        id: doctor.id,
        name: doctorName
      });
    }

    // Process doctors from Users container
    for (const user of doctorUsers) {
      let doctorName = 'Unknown Doctor';
      
      if (user.displayName) {
        doctorName = user.displayName;
      } else if (user.name) {
        doctorName = user.name;
      } else if (user.givenName || user.firstname) {
        const firstName = user.givenName || user.firstname || '';
        const lastName = user.surname || user.lastname || '';
        doctorName = `${firstName} ${lastName}`.trim();
      }

      allDoctors.push({
        id: user.id,
        name: doctorName
      });
    }

    console.log('🔍 Total doctors found:', allDoctors.length);
    return allDoctors;
  }
};
