# Environment Configuration
NODE_ENV=development
PORT=3000

# Azure CosmosDB Configuration
COSMOS_DB_ENDPOINT=https://emr-dev-cosmosdb.documents.azure.com:443/
COSMOS_DB_KEY=****************************************************************************************
COSMOS_DB_DATABASE=ArcaAudioLayer

# API Security
JWT_SECRET=your-jwt-secret-key-here
API_KEY=redash-api-key-2024

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
